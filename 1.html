<!DOCTYPE html>  <！DOCTYPE html>

<html lang="zh-CN">  <html lang=“zh-CN”>
<head>
<meta charset="UTF-8">  <元字符=“UTF-8”>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<元名称=“视口” content=“width=device-width， initial-scale=1.0”>
<title>智能可视化报告系统</title>
<script src="https://cdn.jsdelivr.net/npm/echarts@5.3.3/dist/echarts.min.js"></script>
<脚本 src=“https://cdn.jsdelivr.net/npm/echarts@5.3.3/dist/echarts.min.js”></script>
<style>
body {  正文 {
font-family: 'Segoe UI', 'Microsoft YaHei', 'Helvetica Neue', sans-serif;
字体系列： 'Segoe UI'， 'Microsoft YaHei'， 'Helvetica Neue'， sans-serif;
background-color: #f0f2f5;
背景色：#f0f2f5;
color: #333;  颜色： #333;
display: flex;  显示：flex;
justify-content: center;
justify-content： center;
align-items: center;  对齐项： center;
height: 100vh;  高度：100vh;
margin: 0;  边距： 0;
}
.ppt-slide {  .ppt 滑 {
width: 1280px;  宽度：1280 像素;
height: 720px;  高度：720px;
background-color: #ffffff;
背景色：#ffffff;
border-radius: 10px;  边框半径：10px;
box-shadow: 0 4px 12px rgba(0,0,0,0.15);
框阴影： 0 4px 12px rgba（0,0,0,0.15）;
display: flex;  显示：flex;
flex-direction: column;  flex-direction： 列;
overflow: hidden;  溢出：隐藏;
}
.header {
background: linear-gradient(135deg, #00897b, #006266);
背景： linear-gradient（135deg， #00897b， #006266）;
color: white;  颜色：白色;
padding: 20px 40px;  填充：20px 40px;
display: flex;  显示：flex;
justify-content: space-between;
justify-content： 间距;
align-items: center;  对齐项： center;
}
.header h1 {
font-size: 28px;  字体大小：28px;
margin: 0;  边距： 0;
font-weight: 600;  字体粗细： 600;
}
.logo {
font-size: 20px;  字体大小：20px;
font-weight: bold;  字体粗细：粗体;
}
.main-content {
display: flex;  显示：flex;
flex: 1;  弯曲：1;
padding: 25px;  内边距：25px;
}
.left-panel {
width: 25%;  宽度：25%;
padding-right: 25px;  右填充：25px;
border-right: 1px solid #e0e0e0;
border-right：1px 纯 #e0e0e0;
display: flex;  显示：flex;
flex-direction: column;  flex-direction： 列;
justify-content: space-around;
justify-content： 空间周围;
}
.right-panel {
width: 75%;  宽度：75%;
padding-left: 25px;  左填充：25px;
}
.section {
margin-bottom: 20px;  下边距：20px;
}
.section-title {
font-size: 20px;  字体大小：20px;
font-weight: 600;  字体粗细： 600;
color: #004d40;  颜色： #004d40;
border-bottom: 2px solid #00796b;
border-bottom： 2px 纯色 #00796b;
padding-bottom: 8px;  底部填充：8px;
margin-bottom: 15px;  下边距：15px;
}
.section-content, ul {  .section-content， ul {
font-size: 16px;  字体大小：16px;
line-height: 1.8;  行高：1.8;
padding-left: 20px;  左填充：20px;
}
.module-six {
height: 100%;  高度：100%;
display: flex;  显示：flex;
flex-direction: column;  flex-direction： 列;
}
.module-six .section-title {
font-size: 24px;  字体大小：24px;
text-align: center;  文本对齐：居中;
border-bottom: none;  border-bottom： 无;
color: #006266;  颜色：#006266;
}
.viz-container {
flex: 1;  弯曲：1;
display: grid;  显示：网格;
grid-template-columns: repeat(2, 1fr);
网格模板列： repeat（2， 1fr）;
grid-template-rows: repeat(2, 1fr);
网格模板行： repeat（2， 1fr）;
gap: 20px;  间隙：20px;
margin-top: 15px;  边距顶部：15px;
}
.viz-item {
background-color: #f5f8fa;
背景色：#f5f8fa;
border-radius: 8px;  边框半径：8px;
padding: 15px;  内边距：15px;
display: flex;  显示：flex;
flex-direction: column;  flex-direction： 列;
}
.viz-item h4 {
font-size: 18px;  字体大小：18px;
color: #004d40;  颜色： #004d40;
margin: 0 0 10px 0;
边距： 0 0 10px 0;
display: flex;  显示：flex;
align-items: center;  对齐项： center;
}
.viz-item h4 .item-num {
background-color: #00796b;
背景色：#00796b;
color: white;  颜色：白色;
border-radius: 50%;  border-radius：50%;
width: 28px;  宽度：28px;
height: 28px;  高度：28px;
display: inline-flex;  显示：inline-flex;
justify-content: center;
justify-content： center;
align-items: center;  对齐项： center;
font-size: 14px;  字体大小：14px;
margin-right: 10px;  边距右：10px;
font-weight: bold;  字体粗细：粗体;
}
.viz-item ul {
list-style: none;  列表样式：无;
padding: 0;  填充： 0;
margin: 0;  边距： 0;
flex: 1;  弯曲：1;
}
.viz-item li {
font-size: 14px;  字体大小：14px;
line-height: 1.7;  行高：1.7;
display: flex;  显示：flex;
align-items: center;  对齐项： center;
margin-bottom: 8px;  边距-底部：8px;
}
.viz-item li .icon {
color: #00897b;  颜色： #00897b;
margin-right: 10px;  边距右：10px;
font-weight: bold;  字体粗细：粗体;
font-size: 18px;  字体大小：18px;
width: 20px;  宽度：20px;
text-align: center;  文本对齐：居中;
}
#chart-placeholder {  #chart 占位符 {
border: 1px solid #e0e0e0;
边框：1px 纯色 #e0e0e0;
border-radius: 8px;  边框半径：8px;
display: flex;  显示：flex;
justify-content: center;
justify-content： center;
align-items: center;  对齐项： center;
font-size: 18px;  字体大小：18px;
color: #999;  颜色： #999;
background: #fafafa url('data:image/svg+xml;charset=UTF-8,<svg width="80" height="80" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><path fill="%23e0e0e0" d="M20 80V20h60v60H20zm10-10h40V30H30v40zM45 45h10v10H45V45z"/></svg>') no-repeat center;
背景： #fafafa url（'data：image/svg+xml;charset=UTF-8，<svg width=“80” height=“80” viewBox=“0 0 100 100” xmlns=“http://www.w3.org/2000/svg”><path fill=“%23e0e0e0” d=“M20 80V20h60v60H20zm10-10h40V30H30v40zM45 45h10v10H45V45z”/></svg>'） 无重复中心;
background-size: 50px;  背景大小：50px;
}
.viz-item.full-width {
grid-column: 1 / 3;
网格列：1 / 3;
}
</style>
</head>
<body>
<div class="ppt-slide">  <div class=“ppt-slide”>
<header class="header">  <header class=“header”>
<h1>TEXT COMPANY NAME</h1>
<h1>文本 公司名称</h1>
<div class="logo">LOGO</div>
<div class=“logo”>LOGO</div>
</header>
<main class="main-content">
<main class=“main-content”>
<div class="left-panel">
<div class=“left-panel”>
<div class="section">  
<h3 class="section-title">项目概述</h3>  
<p class="section-content">在此处添加项目概述的具体内容...</p>
</div>  
<div class="section">  
<h3 class="section-title">项目内容</h3>  
<p class="section-content">在此处添加项目内容的具体描述...</p>
</div>  
<div class="section">  
<h3 class="section-title">项目发展</h3>  
<p class="section-content">在此处添加项目发展的规划和愿景...</p>
</div>  
<div class="section">  
<h3 class="section-title">团队介绍</h3>  
<p class="section-content">在此处介绍团队的核心成员和优势...</p>
</div>  
</div>  
<div class="right-panel">  
<div class="module-six">  
<h2 class="section-title">模块六：智能可视化报告系统</h2>
<div class="viz-container">  
<div class="viz-item">  
<h4><span class="item-num">01</span>个人能力报告</h4>
<ul>  
<li><span class="icon">Z</span>多维度能力雷达图，直观展示优势和短板</li>
<li><span class="icon">V</span>历史成长曲线，追踪能力提升轨迹</li>
<li><span class="icon">A</span>个性化改进建议，提供具体提升方案</li>
</ul>  
</div>  
<div class="viz-item">  
<h4><span class="item-num">02</span>企业招聘分析</h4>
<ul>  
<li><span class="icon">?</span>候选人对比分析，支持多人横向比较</li>
<li><span class="icon">7</span>岗位匹配度排序，智能推荐最佳人选</li>
<li><span class="icon">V</span>招聘效果统计，分析招聘ROI和成功率</li>
</ul>  
</div>  
<div class="viz-item">  
<h4><span class="item-num">03</span>行业趋势洞察</h4>
<ul>  
<li><span class="icon">G</span>人才市场趋势分析，预测未来需求</li>
<li><span class="icon">J</span>薪资水平对标，提供市场参考</li>
<li><span class="icon">7</span>技能热度排行，指导培训方向</li>
</ul>  
</div>  
<div class="viz-item">  
<h4><span class="item-num">04</span>可视化技术</h4>  
<ul>  
<li><span class="icon">></span>基于ECharts的丰富图表类型</li>
<li><span class="icon">></span>交互式数据探索，支持钻取和筛选</li>
<li><span class="icon">></span>响应式设计，适配不同设备</li>
</ul>  
</div>  
</div>  
</div>  
</div>  
</main>  
</div>  

</body>  
</html>  

