<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能可视化报告系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(46, 139, 87, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2E8B57 0%, #3CB371 100%);
            color: white;
            text-align: center;
            padding: 40px 20px;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
        }

        .header h1 {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }

        .header .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 40px;
        }

        .section {
            background: #f8fffe;
            border-radius: 15px;
            padding: 30px;
            border: 2px solid #e0f2e0;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .section:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(46, 139, 87, 0.15);
        }

        .section-header {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
        }

        .section-number {
            background: linear-gradient(135deg, #2E8B57, #3CB371);
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5em;
            font-weight: bold;
            margin-right: 15px;
        }

        .section-title {
            font-size: 1.5em;
            color: #2E8B57;
            font-weight: bold;
        }

        .feature-list {
            list-style: none;
        }

        .feature-item {
            background: white;
            margin-bottom: 12px;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #90EE90;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }

        .feature-item:hover {
            border-left-color: #2E8B57;
            transform: translateX(5px);
        }

        .feature-item::before {
            content: '✓';
            color: #2E8B57;
            font-weight: bold;
            margin-right: 10px;
        }

        .bottom-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 0 40px 40px;
        }

        .insight-section {
            background: linear-gradient(135deg, #f0f8f0 0%, #e8f5e8 100%);
            border-radius: 15px;
            padding: 25px;
            border: 2px solid #d0e8d0;
        }

        .insight-title {
            color: #2E8B57;
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .insight-title::before {
            content: '📊';
            margin-right: 10px;
        }

        .tech-title::before {
            content: '🔧';
        }

        .insight-list {
            list-style: none;
        }

        .insight-item {
            padding: 8px 0;
            color: #2d5a2d;
            border-bottom: 1px solid #e0f0e0;
        }

        .insight-item:last-child {
            border-bottom: none;
        }

        .insight-item::before {
            content: '▶';
            color: #2E8B57;
            margin-right: 8px;
            font-size: 0.8em;
        }

        @media (max-width: 768px) {
            .main-content,
            .bottom-section {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .container {
                margin: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>模块六：智能可视化报告系统</h1>
            <div class="subtitle">数据驱动决策，智能洞察未来</div>
        </div>

        <div class="main-content">
            <div class="section">
                <div class="section-header">
                    <div class="section-number">01</div>
                    <div class="section-title">个人能力报告</div>
                </div>
                <ul class="feature-list">
                    <li class="feature-item">多维度能力雷达图，直观展示优势和短板</li>
                    <li class="feature-item">历史成长曲线，追踪能力提升轨迹</li>
                    <li class="feature-item">个性化改进建议，提供具体提升方案</li>
                </ul>
            </div>

            <div class="section">
                <div class="section-header">
                    <div class="section-number">02</div>
                    <div class="section-title">企业招聘分析</div>
                </div>
                <ul class="feature-list">
                    <li class="feature-item">候选人对比分析，支持多人横向比较</li>
                    <li class="feature-item">岗位匹配度排序，智能推荐最佳人选</li>
                    <li class="feature-item">招聘效果统计，分析招聘ROI和成功率</li>
                </ul>
            </div>
        </div>

        <div class="bottom-section">
            <div class="insight-section">
                <div class="insight-title">行业趋势洞察</div>
                <ul class="insight-list">
                    <li class="insight-item">人才市场趋势分析，预测未来需求</li>
                    <li class="insight-item">薪资水平对标，提供市场参考</li>
                    <li class="insight-item">技能热度排行，指导培训方向</li>
                </ul>
            </div>

            <div class="insight-section">
                <div class="insight-title tech-title">可视化技术</div>
                <ul class="insight-list">
                    <li class="insight-item">基于ECharts的丰富图表类型</li>
                    <li class="insight-item">交互式数据探索，支持钻取和筛选</li>
                    <li class="insight-item">响应式设计，适配不同设备</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
